'use strict';

const EnhancedStorageDeviceService = require('../service/enhancedStorageDeviceService');
const { logger } = require('ee-core/log');
const { getMainWindow } = require('ee-core/electron');

/**
 * 存储设备控制器
 * 提供存储设备相关的IPC接口
 */
class StorageController {
  constructor() {
    this.storageService = EnhancedStorageDeviceService.getInstance();
    this._setupEventListeners();
  }

  /**
   * 设置事件监听器
   * @private
   */
  _setupEventListeners() {
    // 监听设备连接事件
    this.storageService.on('device-connected', (device) => {
      this._sendToRenderer('storage:device-connected', device);
      logger.info(`[StorageController] Device connected: ${device.label}`);
    });

    // 监听设备断开事件
    this.storageService.on('device-disconnected', (device) => {
      this._sendToRenderer('storage:device-disconnected', device);
      logger.info(`[StorageController] Device disconnected: ${device.label}`);
    });

    // 监听设备列表更新事件
    this.storageService.on('devices-updated', (devices) => {
      this._sendToRenderer('storage:devices-updated', devices);
    });

    // 监听监控状态变化
    this.storageService.on('monitoring-started', () => {
      this._sendToRenderer('storage:monitoring-started');
    });

    this.storageService.on('monitoring-stopped', () => {
      this._sendToRenderer('storage:monitoring-stopped');
    });
  }

  /**
   * 向渲染进程发送消息
   * @private
   */
  _sendToRenderer(channel, data) {
    try {
      const mainWindow = getMainWindow();
      if (mainWindow && !mainWindow.isDestroyed()) {
        mainWindow.webContents.send(channel, data);
      }
    } catch (error) {
      logger.error('[StorageController] Failed to send message to renderer:', error);
    }
  }

  /**
   * 获取所有存储设备
   * @param {Object} args - 参数对象
   * @returns {Promise<Array>} 设备列表
   */
  async getDevices(args = {}) {
    try {
      const devices = await this.storageService.getDevices();
      
      // 可选：根据参数过滤设备
      if (args.onlyRemovable) {
        return devices.filter(device => device.isRemovable);
      }
      
      if (args.onlyWritable) {
        const writableDevices = [];
        for (const device of devices) {
          const isWritable = await this.storageService.isDeviceWritable(device.mountPoint);
          if (isWritable) {
            device.isWritable = true;
            writableDevices.push(device);
          }
        }
        return writableDevices;
      }
      
      return devices;
    } catch (error) {
      logger.error('[StorageController] Failed to get devices:', error);
      return [];
    }
  }

  /**
   * 获取设备详细信息
   * @param {Object} args - 参数对象
   * @param {string} args.deviceId - 设备ID
   * @returns {Promise<Object|null>} 设备信息
   */
  async getDeviceInfo(args) {
    try {
      const { deviceId } = args;
      if (!deviceId) {
        throw new Error('Device ID is required');
      }
      
      const device = this.storageService.getDeviceInfo(deviceId);
      if (device) {
        // 检查设备是否可写
        device.isWritable = await this.storageService.isDeviceWritable(device.mountPoint);
        
        // 格式化大小信息
        device.formattedTotalSize = this.storageService.formatSize(device.totalSize);
        device.formattedFreeSpace = this.storageService.formatSize(device.freeSpace);
        device.formattedUsedSpace = this.storageService.formatSize(device.usedSpace);
      }
      
      return device;
    } catch (error) {
      logger.error('[StorageController] Failed to get device info:', error);
      return null;
    }
  }

  /**
   * 启动设备监控
   * @returns {Promise<Object>} 操作结果
   */
  async startMonitoring() {
    try {
      await this.storageService.startMonitoring();
      return { success: true, message: 'Device monitoring started' };
    } catch (error) {
      logger.error('[StorageController] Failed to start monitoring:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * 停止设备监控
   * @returns {Object} 操作结果
   */
  stopMonitoring() {
    try {
      this.storageService.stopMonitoring();
      return { success: true, message: 'Device monitoring stopped' };
    } catch (error) {
      logger.error('[StorageController] Failed to stop monitoring:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * 检查设备是否可写
   * @param {Object} args - 参数对象
   * @param {string} args.mountPoint - 挂载点
   * @returns {Promise<Object>} 检查结果
   */
  async checkDeviceWritable(args) {
    try {
      const { mountPoint } = args;
      if (!mountPoint) {
        throw new Error('Mount point is required');
      }
      
      const isWritable = await this.storageService.isDeviceWritable(mountPoint);
      return { success: true, isWritable };
    } catch (error) {
      logger.error('[StorageController] Failed to check device writable:', error);
      return { success: false, error: error.message, isWritable: false };
    }
  }

  /**
   * 格式化文件大小
   * @param {Object} args - 参数对象
   * @param {number} args.bytes - 字节数
   * @returns {string} 格式化后的大小
   */
  formatSize(args) {
    try {
      const { bytes } = args;
      return this.storageService.formatSize(bytes || 0);
    } catch (error) {
      logger.error('[StorageController] Failed to format size:', error);
      return '0 B';
    }
  }

  /**
   * 获取监控状态
   * @returns {Object} 监控状态
   */
  getMonitoringStatus() {
    return {
      isMonitoring: this.storageService.isMonitoring,
      deviceCount: this.storageService.devices.size
    };
  }

  /**
   * 刷新设备列表
   * @returns {Promise<Array>} 设备列表
   */
  async refreshDevices() {
    try {
      // 强制重新扫描设备
      await this.storageService._scanDevices();
      return await this.getDevices();
    } catch (error) {
      logger.error('[StorageController] Failed to refresh devices:', error);
      return [];
    }
  }
}

module.exports = StorageController;
