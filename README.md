<div align=center>
<h3>拓疆者操作台辅助屏</h3>
</div>
<br>

<div align=center>
<img src="./public/images/logo.png" width="150" height="150" />
</div>
<br>

## 介绍

该项目是基于Electron-egg框架开发的跨平台桌面应用，主要功能是作为"拓疆者操作台"的辅助显示屏。技术栈采用Electron + Vue3 + Vuetify实现，支持Windows、Mac和Linux多平台打包。

关键特点：

1. 使用electron-egg框架管理应用生命周期
2. 前端基于Vue3组合式API开发，搭配Vuetify实现Material Design界面
3. 支持ARM64架构的Linux系统部署（通过.deb包安装）
4. 包含完整的构建流水线（dev/build/package）
5. 采用多进程架构，electron主进程与渲染进程分离

主要模块：

- 主进程入口：public/electron/main.js
- 前端入口：frontend/src/App.vue
- 构建脚本：package.json中的多平台build命令

## 使用

打包之前先构建
npm run build

打包(普通linux)
docker run --rm -ti -e ELECTRON_MIRROR="https://npmmirror.com/mirrors/electron/" -v ${PWD}:/project electronuserland/builder bash -c "npm install && npm run build-l"

打包(平板)
docker run --rm -ti -e ELECTRON_MIRROR="https://npmmirror.com/mirrors/electron/" -v ${PWD}:/project electronuserland/builder bash -c "npm install && npm run build-l-arm64"

原生打包
USE_SYSTEM_FPM=true npm run build-l-arm64

拷贝到平板
scp .\out\bux-aux-linux-0.0.66-arm64.deb ztl@192.168.71.83:~/
连接平板

// 有打包环境
ssh ztl@192.168.70.106
// 操作台
ssh ztl@192.168.71.167

安装软件
sudo dpkg -i bux-aux-linux-0.0.91-arm64.deb

查找软件
dpkg -l | grep " bux-aux "

删除软件
sudo dpkg -r bux-aux

删除配置
sudo dpkg -P bux-aux

## 平板参数
minimum 320 x 200, current 800 x 1280, maximum 8192 x 8192

## 日志地址
/home/<USER>/.bux-aux/logs
## 配置文件
/home/<USER>/.config/bux-aux