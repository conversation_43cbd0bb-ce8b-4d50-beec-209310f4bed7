<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>拓疆者辅助屏幕</title>
    <style>
      #app {
        min-height: 100vh;
        width: 100%;
      }
      /* 添加全局滚动条样式 */
      body {
        overflow-x: hidden !important;
        overflow-y: hidden !important;
        user-select: none!important;
      }
      /* 隐藏所有滚动条 */
      ::-webkit-scrollbar {
        display: none !important;
      }      
    </style>
  </head>
  <body>
    <div id="app"></div>
    <script type="module" src="/src/main.js"></script>
  </body>
</html>
