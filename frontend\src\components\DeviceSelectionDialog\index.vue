<template>
  <v-dialog
    v-model="dialogVisible"
    max-width="900"
    persistent
    @update:model-value="handleDialogUpdate"
  >
    <v-card>
      <v-card-title class="d-flex align-center justify-space-between">
        <span class="text-h6">选择下载目标位置</span>
        <v-btn
          icon="mdi-refresh"
          variant="text"
          size="small"
          @click="refreshDevices"
          :loading="refreshing"
          title="刷新设备列表"
        ></v-btn>
      </v-card-title>

      <v-card-text>
        <div v-if="loading" class="text-center py-8">
          <v-progress-circular indeterminate color="primary"></v-progress-circular>
          <div class="mt-2">正在扫描存储设备...</div>
        </div>

        <div v-else-if="error" class="text-center py-8">
          <v-icon color="error" size="48">mdi-alert-circle</v-icon>
          <div class="mt-2 text-error">{{ error }}</div>
          <v-btn color="primary" variant="outlined" class="mt-4" @click="refreshDevices">
            重试
          </v-btn>
        </div>

        <div v-else-if="devices.length === 0" class="text-center py-8">
          <v-icon color="warning" size="48">mdi-harddisk-remove</v-icon>
          <div class="mt-2">未找到可用的存储设备</div>
          <div class="text-caption text-medium-emphasis mt-1">
            请插入U盘或确保硬盘分区可访问
          </div>
        </div>

        <div v-else>
          <div class="mb-4">
            <v-chip
              :color="monitoringStatus ? 'success' : 'warning'"
              size="small"
              class="mr-2"
            >
              <v-icon start>{{ monitoringStatus ? 'mdi-eye' : 'mdi-eye-off' }}</v-icon>
              {{ monitoringStatus ? '实时监控中' : '监控已停止' }}
            </v-chip>
            <span class="text-caption">找到 {{ devices.length }} 个存储设备</span>
          </div>

          <!-- 设备分类显示 -->
          <div v-for="category in deviceCategories" :key="category.name" class="mb-4">
            <v-divider v-if="category.name !== deviceCategories[0].name" class="mb-3"></v-divider>

            <div class="d-flex align-center mb-2">
              <v-icon :color="category.color" class="mr-2">{{ category.icon }}</v-icon>
              <span class="text-subtitle-2 font-weight-medium">{{ category.name }}</span>
              <v-chip size="x-small" class="ml-2" :color="category.color" variant="outlined">
                {{ category.devices.length }}
              </v-chip>
              <v-chip
                v-if="category.recommended"
                size="x-small"
                color="primary"
                class="ml-2"
              >
                推荐
              </v-chip>
            </div>

            <v-list density="compact" class="device-category-list">
              <v-list-item
                v-for="device in category.devices"
                :key="device.id"
                :value="device.id"
                @click="selectDevice(device)"
                :class="{ 'selected-device': selectedDevice?.id === device.id }"
                class="device-item"
              >
              <template v-slot:prepend>
                <v-avatar :color="getDeviceColor(device)" size="40">
                  <v-icon :icon="getDeviceIcon(device)" color="white"></v-icon>
                </v-avatar>
              </template>

              <v-list-item-title class="font-weight-medium">
                {{ device.label }}
                <v-chip
                  v-if="device.isRemovable"
                  size="x-small"
                  color="primary"
                  class="ml-2"
                >
                  可移动
                </v-chip>
              </v-list-item-title>

              <v-list-item-subtitle>
                <div class="d-flex align-center">
                  <span>{{ device.mountPoint }}</span>
                  <v-chip
                    v-if="device.isWritable === false"
                    size="x-small"
                    color="error"
                    class="ml-2"
                  >
                    只读
                  </v-chip>
                </div>
                <div class="mt-1">
                  <span class="text-caption">
                    总容量: {{ formatSize(device.totalSize) }} | 
                    可用: {{ formatSize(device.freeSpace) }} | 
                    已用: {{ formatSize(device.usedSpace) }}
                  </span>
                </div>
                <v-progress-linear
                  :model-value="getUsagePercentage(device)"
                  :color="getUsageColor(device)"
                  height="4"
                  class="mt-1"
                ></v-progress-linear>
              </v-list-item-subtitle>

              <template v-slot:append>
                <v-radio-group v-model="selectedDeviceId" hide-details>
                  <v-radio :value="device.id"></v-radio>
                </v-radio-group>
              </template>
            </v-list-item>
          </v-list>
        </div>
        </div>
      </v-card-text>

      <v-card-actions>
        <v-spacer></v-spacer>
        <v-btn
          variant="text"
          @click="handleCancel"
        >
          取消
        </v-btn>
        <v-btn
          color="primary"
          variant="elevated"
          @click="handleConfirm"
          :disabled="!selectedDevice || selectedDevice.isWritable === false"
        >
          确认选择
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script setup>
import { ref, computed, watch, onMounted, onUnmounted } from 'vue';
import { ipc } from '@/utils/ipcRenderer';

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  }
});

// Emits
const emit = defineEmits(['update:modelValue', 'device-selected', 'cancel']);

// 响应式数据
const devices = ref([]);
const selectedDeviceId = ref('');
const loading = ref(false);
const refreshing = ref(false);
const error = ref('');
const monitoringStatus = ref(false);

// 计算属性
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
});

const selectedDevice = computed(() => {
  return devices.value.find(device => device.id === selectedDeviceId.value) || null;
});

// 设备分类
const deviceCategories = computed(() => {
  const categories = [
    {
      name: '推荐设备',
      icon: 'mdi-star',
      color: 'primary',
      recommended: true,
      devices: []
    },
    {
      name: '可移动设备',
      icon: 'mdi-usb-flash-drive',
      color: 'success',
      recommended: false,
      devices: []
    },
    {
      name: '本地磁盘',
      icon: 'mdi-harddisk',
      color: 'info',
      recommended: false,
      devices: []
    },
    {
      name: '其他位置',
      icon: 'mdi-folder',
      color: 'warning',
      recommended: false,
      devices: []
    }
  ];

  // 分类设备
  devices.value.forEach(device => {
    // 推荐设备（高推荐级别且可写）
    if (device.recommendLevel >= 70 && device.isWritable) {
      categories[0].devices.push(device);
    }
    // 可移动设备
    else if (device.isRemovable) {
      categories[1].devices.push(device);
    }
    // 本地磁盘
    else if (device.type === 'fixed' || device.type === 'system') {
      categories[2].devices.push(device);
    }
    // 其他位置
    else {
      categories[3].devices.push(device);
    }
  });

  // 过滤掉空分类
  return categories.filter(category => category.devices.length > 0);
});

// 方法
const loadDevices = async () => {
  loading.value = true;
  error.value = '';
  
  try {
    // 获取设备列表
    const deviceList = await ipc.invoke('controller/storage/getDevices', { onlyWritable: false });
    
    // 为每个设备检查可写状态
    for (const device of deviceList) {
      const writeCheck = await ipc.invoke('controller/storage/checkDeviceWritable', {
        mountPoint: device.mountPoint
      });
      device.isWritable = writeCheck.isWritable;
    }
    
    devices.value = deviceList;
    
    // 获取监控状态
    const status = await ipc.invoke('controller/storage/getMonitoringStatus');
    monitoringStatus.value = status.isMonitoring;
    
  } catch (err) {
    console.error('Failed to load devices:', err);
    error.value = '加载存储设备失败: ' + (err.message || '未知错误');
  } finally {
    loading.value = false;
  }
};

const refreshDevices = async () => {
  refreshing.value = true;
  try {
    await ipc.invoke('controller/storage/refreshDevices');
    await loadDevices();
  } catch (err) {
    console.error('Failed to refresh devices:', err);
    error.value = '刷新设备列表失败: ' + (err.message || '未知错误');
  } finally {
    refreshing.value = false;
  }
};

const selectDevice = (device) => {
  if (device.isWritable !== false) {
    selectedDeviceId.value = device.id;
  }
};

const handleConfirm = () => {
  if (selectedDevice.value) {
    emit('device-selected', selectedDevice.value);
    dialogVisible.value = false;
  }
};

const handleCancel = () => {
  emit('cancel');
  dialogVisible.value = false;
};

const handleDialogUpdate = (value) => {
  if (value) {
    // 对话框打开时加载设备
    loadDevices();
    selectedDeviceId.value = '';
  }
};

// 工具方法
const formatSize = (bytes) => {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

const getDeviceIcon = (device) => {
  // 根据设备类型和特征返回合适的图标
  if (device.isRemovable) {
    if (device.type === 'cdrom') return 'mdi-disc';
    if (device.volumeName?.toLowerCase().includes('usb')) return 'mdi-usb-flash-drive';
    return 'mdi-usb-flash-drive-outline';
  }

  if (device.type === 'system') return 'mdi-harddisk';
  if (device.type === 'user') return 'mdi-folder-account';
  if (device.type === 'network') return 'mdi-server-network';
  if (device.platform === 'linux' && device.mountPoint.startsWith('/media')) return 'mdi-usb-flash-drive';
  if (device.platform === 'linux' && device.mountPoint.startsWith('/mnt')) return 'mdi-harddisk-plus';

  return 'mdi-harddisk';
};

const getDeviceColor = (device) => {
  // 根据设备状态和推荐级别返回颜色
  if (device.isWritable === false) return 'error';

  if (device.recommendLevel >= 70) return 'success';
  if (device.isRemovable) return 'primary';
  if (device.type === 'system') return 'warning';

  return 'info';
};

const getUsagePercentage = (device) => {
  if (device.totalSize === 0) return 0;
  return (device.usedSpace / device.totalSize) * 100;
};

const getUsageColor = (device) => {
  const percentage = getUsagePercentage(device);
  if (percentage > 90) return 'error';
  if (percentage > 70) return 'warning';
  return 'success';
};

// 设备事件监听
const setupDeviceListeners = () => {
  ipc.on('storage:device-connected', (_, device) => {
    console.log('Device connected:', device);
    loadDevices(); // 重新加载设备列表
  });

  ipc.on('storage:device-disconnected', (_, device) => {
    console.log('Device disconnected:', device);
    loadDevices(); // 重新加载设备列表
  });

  ipc.on('storage:devices-updated', (_, deviceList) => {
    console.log('Devices updated:', deviceList);
    // 可以选择直接更新或重新加载
    loadDevices();
  });
};

const removeDeviceListeners = () => {
  ipc.removeAllListeners('storage:device-connected');
  ipc.removeAllListeners('storage:device-disconnected');
  ipc.removeAllListeners('storage:devices-updated');
};

// 生命周期
onMounted(() => {
  setupDeviceListeners();
});

onUnmounted(() => {
  removeDeviceListeners();
});
</script>

<style scoped>
.device-list {
  max-height: 400px;
  overflow-y: auto;
}

.device-item {
  border: 2px solid transparent;
  border-radius: 8px;
  margin-bottom: 8px;
  transition: all 0.2s ease;
}

.device-item:hover {
  background-color: rgba(var(--v-theme-primary), 0.04);
}

.selected-device {
  border-color: rgb(var(--v-theme-primary));
  background-color: rgba(var(--v-theme-primary), 0.08);
}

.device-item:last-child {
  margin-bottom: 0;
}
</style>
