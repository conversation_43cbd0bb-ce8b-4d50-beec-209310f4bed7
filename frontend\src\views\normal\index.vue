<template>
  <div>
    <v-card class="content-card">
      <div class="sidebar-left">
        <div class="text-[32px] text-[#ddd] bg-[#333] h-12 px-5 rounded-[16px] leading-[48px]">
          {{ currentTime }}
        </div>
        <div class="text-[32px] text-[#ddd] bg-[#333] h-12 px-5 rounded-[16px] leading-[46px] mt-3">
          <span class="mr-5">上次装车</span>{{ lastLoadingTime }}
        </div>
        <div class="text-[32px] text-[#ddd] bg-[#333] h-12 px-5 rounded-[16px] leading-[46px] mt-3">
          <div class="text-[28px] text-gray-100">
            <span class="mr-1 inline-block w-[184px]">{{ pageData.group_name }}已装: </span
            ><span class="text-[#fff]">{{ pageData.count }}</span> 车
          </div>
        </div>
      </div>
      <div class="h-[70px] flex justify-between items-center px-5">
        <div class="flex-1"></div>
        <div class="flex-1 w-[200px] text-center text-[32px] text-[#ddd]">
          {{ configData.vehicle_name }}
        </div>
        <div class="flex-1 py-2 flex flex-row justify-end">
          <div
            class="h-12 w-24 text-center leading-[48px] rounded-[16px] text-[#ddd] bg-[#2a2a2a] cursor-pointer mr-3"
            @click="reportDialog = true"
          >
            报告
          </div>
          <div
            class="h-12 w-24 text-center leading-[48px] rounded-[16px] text-[#ddd] bg-[#2a2a2a] cursor-pointer mr-3"
            @click="troubleshootingDialog = true"
          >
            补卡
          </div>
          <!-- <div class="h-12 w-24 text-center leading-[48px] rounded-[16px] text-[#ddd] bg-[#2a2a2a] cursor-pointer mr-3">
            排行榜
          </div> -->
          <div
            class="h-12 w-24 text-center leading-[48px] rounded-[16px] text-[#ddd] bg-[#2a2a2a] cursor-pointer"
            @click="settingsDialog = true"
          >
            设 置
          </div>
        </div>
      </div>
      <div class="loading-car-container">
        <div class="loading-car-buttons">
          <v-btn
            v-if="configData.mode === 'complex'"
            color="error"
            size="x-large"
            class="loading-car-btn"
            prepend-icon="mdi-play-circle"
          >
            车辆进站
          </v-btn>
          <div
            color="success"
            size="x-large"
            class="loading-car-btn relative"
            @mousedown="startPress"
            @mouseup="cancelPress"
            @mouseleave="cancelPress"
            @touchstart.prevent="startPress"
            @touchend="cancelPress"
          >
            <div class="charge-progress" :style="{ height: pressProgress + '%' }"></div>
            <div class="text-slate-50 mt-5 flex items-center flex-col">
              <img class="w-[170px] h-[100px] mb-[30px]" :src="muyu" :class="{ 'muyu-animation': showPlusOne }" />
              完成装车
            </div>
            <span v-if="showPlusOne" class="plus-one-animation">+1</span>
          </div>
          <v-btn
            v-if="configData.mode === 'complex'"
            color="warning"
            size="x-large"
            class="loading-car-btn"
            prepend-icon="mdi-arrow-up-circle"
          >
            开始装车
          </v-btn>
          <v-btn
            v-if="configData.mode === 'complex'"
            color="info"
            size="x-large"
            class="loading-car-btn"
            prepend-icon="mdi-arrow-down-circle"
          >
            装车斗数
          </v-btn>
        </div>
      </div>
      <div class="flex flex-row items-center h-[160px] mx-5 my-5 bg-[#333333] rounded-[16px] px-2 py-1">
        <!-- <div class="flex-1 text-white p-1 text-lg h-full mr-2">
          <div class="h-[30px] mb-1 text-base text-white flex justify-between items-center">
            <div>最近装车记录</div>
            <div
              class="bg-[#444444] px-2 py-1 rounded-lg cursor-pointer text-[#dddddd] text-sm"
              @click="openHistoryDialog('load')"
            >
              历史记录
            </div>
          </div>
          <div class="py-1 h-[calc(100%-30px)]">
            <transition-group class="flex flex-col gap-2" v-if="showRecentLoadData.length > 0" name="scroll" tag="div">
              <div
                v-for="item in showRecentLoadData"
                :key="item.timestamp"
                class="bg-[#444444] p-[5px] rounded-lg flex justify-between items-center text-lg"
              >
                <div class="text-sm flex-grow text-center">{{ item.group_name }}</div>
                <div class="text-sm flex-grow text-center">{{ item.driver_name }}</div>
                <div class="text-sm flex-grow text-center">距离上车: {{ item.duration }}</div>
                <div class="text-xs text-gray-400 text-center mr-4">
                  {{ dayjs(item.timestamp).format("HH:mm:ss") }}
                </div>
              </div>
            </transition-group>
            <div v-else class="text-center text-gray-500 h-full flex items-center justify-center">暂无装车记录</div>
          </div>
        </div> -->
        <div class="flex-1 text-white p-1 text-lg h-full">
          <div class="h-[30px] mb-1 text-base text-white flex justify-between items-center">
            <div>最近班组记录</div>
            <div
              class="bg-[#444444] px-2 py-1 rounded-lg cursor-pointer text-[#dddddd] text-sm"
              @click="openHistoryDialog('team')"
            >
              历史记录
            </div>
          </div>
          <div class="flex flex-col gap-2 py-1 h-[calc(100%-30px)]">
            <template v-if="showRecentTeamData.length > 0">
              <div
                v-for="(item, index) in showRecentTeamData"
                :key="index"
                class="bg-[#444444] p-[5px] rounded-lg flex justify-between items-center text-lg"
              >
                <!-- <div class="text-sm text-lime-400 w-12 text-center">#{{ index + 1 }}</div> -->
                <div class="text-sm flex-grow text-center">{{ item.group_name }}</div>
                <!-- <div class="text-sm flex-grow text-center">{{ item.vehicle_name }}</div> -->
                <!-- <div class="text-sm flex-grow text-center">{{ item.driver_name }}</div> -->
                <div class="text-sm flex-grow text-center">装车数量: {{ item.count }} 车</div>
                <div class="text-xs text-gray-400 text-center mr-4">
                  {{ dayjs(item.latest_timestamp).format("YYYY-MM-DD HH:mm") }}
                </div>
              </div>
            </template>
            <div v-else class="text-center text-gray-500 h-full flex items-center justify-center">暂无班组记录</div>
          </div>
        </div>
      </div>
    </v-card>
    <SettingsDialog v-model="settingsDialog" :config-data="configData" @save="saveConfig" />
    <HistoryDialog v-model="historyDialog" :history-type="historyType" :vehicle-id="pageData.vehicle_id" />
    <TroubleshootingDialog v-model="troubleshootingDialog" @save="saveTroubleshooting" />
    <v-dialog v-model="reportDialog" fullscreen>
      <v-card class="relative">
        <div class="absolute top-3 right-4">
          <v-btn color="primary" size="large" @click="reportDialog = false">返回</v-btn>
        </div>
        <iframe
          :src="`https://oms.apps.builderx.com/report?vehicle_id=${pageData.vehicle_id}&from_platform=pad&token=t-05393aef3c9871ec3eb5759fbde7c329d668d05433eb`"
          frameborder="0"
          style="width: 100%; height: 100%"
        ></iframe>
      </v-card>
    </v-dialog>
  </div>
</template>

<script setup>
import dayjs from "dayjs";
import customParseFormat from "dayjs/plugin/customParseFormat";
dayjs.extend(customParseFormat);
import confetti from "canvas-confetti";
import Keyboard from "@/components/Keyboard/index.vue";
import HistoryDialog from "./components/HistoryDialog.vue";
import TroubleshootingDialog from "./components/TroubleshootingDialog.vue";
import SettingsDialog from "./components/SettingsDialog.vue";
import muyu from "@/assets/image/muyu.png";
import completeAudio from "@/assets/audio/complete.mp3";
import { ipc } from "@/utils/ipcRenderer";

import { reactive, ref, computed, onMounted, onUnmounted } from "vue";
import { reportEventApi, getVehicleConfigApi, setVehicleConfigApi, getRecentTeamApi } from "@/api/omsApi";

const settingsDialog = ref(false);
const reportDialog = ref(false);
const troubleshootingDialog = ref(false);
const showPlusOne = ref(false);
const historyDialog = ref(false);
const historyType = ref("load");

const openHistoryDialog = (type) => {
  historyType.value = type;
  historyDialog.value = true;
};

const pageData = reactive({
  vehicle_id: "66f135aa8ea96a77201f53e9",
  vehicle_name: "测试机器",
  group_name: "班组A",
  driver_name: "builderx",
  count: 0,
});

const configData = reactive({
  mode: "simple", // simple complex
  vehicle_name: "电铲",
  load_threshold: 60,
  group: [],
});

// 展示最近3条数据
const showRecentLoadData = ref([]);

// 展示装车排行
const showRecentTeamData = ref([]);

// 显示当前时间 例如 2020年10月10日10时10分10秒
const now = ref(new Date());
let timer = null;

const currentTime = computed(() => {
  return dayjs(now.value).format("MM月DD日 HH:mm:ss");
});

const lastLoadingTime = computed(() => {
  if (showRecentLoadData.value.length > 0) {
    return dayjs(showRecentLoadData.value[showRecentLoadData.value.length - 1].timestamp).format("HH:mm:ss");
  }
  return "--";
});

const getVehicleConfig = async () => {
  // TODO: 从全局获取车辆ID
  pageData.vehicle_id = await ipc.invoke("global-state:get", "vehicle_id");

  console.log(pageData.vehicle_id);

  const { data } = await getVehicleConfigApi(pageData.vehicle_id);
  if (data) {
    configData.mode = data.mode;
    configData.load_threshold = data.load_threshold;
    configData.group = data.group;
    configData.vehicle_name = data.name;
  }
  await checkData();
};

const saveConfig = async (newConfig) => {
  Object.assign(configData, newConfig);
  const { data } = await setVehicleConfigApi(pageData.vehicle_id, {
    vehicle_id: pageData.vehicle_id,
    mode: newConfig.mode,
    load_threshold: newConfig.load_threshold,
    group: newConfig.group,
    name: newConfig.vehicle_name,
  });
  if (data) {
    settingsDialog.value = false;
  }
  checkData();
};

const getRecentTeamData = async () => {
  const { data } = await getRecentTeamApi(pageData.vehicle_id);
  if (data) {
    showRecentTeamData.value = data.lists.slice(0, 3);
  }
};

onMounted(async () => {
  timer = setInterval(() => {
    now.value = new Date();
  }, 1000);
  await getVehicleConfig();
  await getRecentTeamData();
});

onUnmounted(() => {
  clearInterval(timer);
});

const createBaseRecord = () => {
  const now = dayjs();
  return {
    timestamp: now.valueOf(), // 使用时间戳作为key
    vehicle_id: pageData.vehicle_id,
    vehicle_name: pageData.vehicle_name,
    group_name: pageData.group_name,
    driver_name: pageData.driver_name,
    mode: configData.mode,
  };
};

const pressTimer = ref(null);
const pressProgress = ref(0);
let progressInterval = null;

const startPress = () => {
  pressProgress.value = 0;
  if (progressInterval) clearInterval(progressInterval);
  progressInterval = setInterval(() => {
    if (pressProgress.value < 100) {
      pressProgress.value += 2;
    } else {
      clearInterval(progressInterval);
    }
  }, 6);

  if (pressTimer.value) clearTimeout(pressTimer.value);
  pressTimer.value = setTimeout(() => {
    eventReport();
    cancelPress(true);
  }, 300);
};

const cancelPress = (isSuccess = false) => {
  clearTimeout(pressTimer.value);
  clearInterval(progressInterval);

  if (!isSuccess) {
    const rollbackInterval = setInterval(() => {
      if (pressProgress.value > 0) {
        pressProgress.value -= 4;
      } else {
        pressProgress.value = 0;
        clearInterval(rollbackInterval);
      }
    }, 10);
  } else {
    pressProgress.value = 0;
  }
};

const eventReport = async () => {
  const audio = new Audio(completeAudio);
  audio.play();
  confetti({ particleCount: 60, spread: 200, origin: { y: 0.7 } });
  showPlusOne.value = true;
  setTimeout(() => {
    showPlusOne.value = false;
  }, 1000);
  pageData.count++;

  const newRecord = createBaseRecord();

  try {
    const itemLog = {
      ...newRecord,
      event: "finish",
    };
    await reportEventApi(itemLog);
  } catch (error) {
    console.error("上报事件失败", error);
  }

  let duration = "0分0秒";
  if (showRecentLoadData.value.length > 0) {
    const lastRecordTime = dayjs(showRecentLoadData.value[showRecentLoadData.value.length - 1].timestamp);
    const diffInSeconds = dayjs().diff(lastRecordTime, "second");
    const minutes = Math.floor(diffInSeconds / 60);
    const seconds = diffInSeconds % 60;
    duration = `${minutes}分${seconds}秒`;
  }

  newRecord.duration = duration;

  showRecentLoadData.value.push(newRecord);
  if (showRecentLoadData.value.length > 3) {
    showRecentLoadData.value.shift();
  }

  getRecentTeamData();
};

const saveTroubleshooting = async (formdata) => {
  const newRecord = createBaseRecord();
  const { type, data } = formdata;
  if (type === "troubleshooting") {
    try {
      const itemLog = {
        ...newRecord,
        event: "trouble",
        start_time: data.startTime,
        end_time: data.endTime,
        content: data.content,
      };
      itemLog.timestamp = dayjs(`${data.date} ${data.startTime}`).valueOf();
      await reportEventApi(itemLog);
    } catch (error) {
      console.error("上报事件失败", error);
    }
  } else if (type === "reissueCard") {
    try {
      const itemLog = {
        ...newRecord,
        event: "replenish",
      };
      itemLog.timestamp = dayjs(`${data.date} ${data.time}`).valueOf();
      await reportEventApi(itemLog);
    } catch (error) {
      console.error("上报事件失败", error);
    }
  }
};

const checkData = () => {
  if (configData.group.length === 0) {
    return;
  }
  if (configData.group.length === 1) {
    pageData.group_name = configData.group[0].name;
    pageData.driver_name = configData.group[0].driver_name;
    return;
  }
  const now = dayjs();
  for (const group of configData.group) {
    let startTime = dayjs(group.start_time, "HH:mm");
    let endTime = dayjs(group.end_time, "HH:mm");

    if (endTime.isBefore(startTime)) {
      if (now.isAfter(startTime) || now.isBefore(endTime)) {
        pageData.group_name = group.name;
        pageData.driver_name = group.driver_name;
        return;
      }
    } else {
      if (now.isAfter(startTime) && now.isBefore(endTime)) {
        pageData.group_name = group.name;
        pageData.driver_name = group.driver_name;
        return;
      }
    }
  }
};
</script>

<style scoped>
.scroll-move,
.scroll-enter-active,
.scroll-leave-active {
  transition: all 0.5s ease;
}

.scroll-enter-from,
.scroll-leave-to {
  opacity: 0;
  transform: translateY(36px);
}

.scroll-leave-active {
  position: absolute;
}

.content-card {
  position: relative;
  height: calc(100vh - var(--app-bar-height));
  border-radius: 0;
  background-color: #090909;
  display: flex;
  flex-direction: column;
}

.sidebar-left {
  position: absolute;
  top: 20px;
  left: 20px;
  height: 40%;
  border-radius: 16px;
}

.loading-car-container {
  display: flex;
  justify-content: center;
  align-items: center;
  flex: 1;
  padding: 30px;
}

.loading-car-buttons {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 40px;
  width: 100%;
  max-width: 1000px;
}

/* 当只有一个按钮时，使其居中 */
.loading-car-buttons:has(> .loading-car-btn:first-child:last-child) {
  grid-template-columns: 1fr;
  justify-items: center;

  .loading-car-btn {
    height: 350px;
    width: 350px;
    border-radius: 24%;
    font-size: 40px;
    background-color: #444444 !important;
  }
}

.loading-car-btn {
  height: 150px;
  font-size: 30px;
  font-weight: 500;
  border-radius: 12px;
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
}

.charge-progress {
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100%;
  background-color: rgba(255, 255, 255, 0.4);
  transition: width 0.05s linear;
  z-index: 1;
}

.loading-car-btn > .v-btn__content {
  position: relative;
  z-index: 2;
}

.plus-one-animation {
  position: absolute;
  top: 70px;
  right: 60px;
  font-size: 60px;
  color: #11ec19;
  animation: plus-one 1s ease-out;
  font-weight: 800;
}

@keyframes plus-one {
  0% {
    transform: translateY(0) scale(1);
    opacity: 1;
  }

  100% {
    transform: translateY(-60px) scale(1.5);
    opacity: 0;
  }
}

.loading-car-btn.relative {
  position: relative;
}

.muyu-animation {
  animation: scale-in-out 0.5s ease-in-out;
}

@keyframes scale-in-out {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(0.8);
  }
  100% {
    transform: scale(1);
  }
}
</style>
