<template>
  <div class="config-container">
    <BackButton />
    <div v-for="(item, index) in buttonConfigs" :key="item.id" class="config-item">
      <div class="config-header">
        <span>配置项 {{ index + 1 }}</span>
        <div class="d-flex align-center">
          <v-btn icon @click="moveUp(index)" :disabled="index === 0">
            <v-icon>mdi-arrow-up</v-icon>
          </v-btn>
          <v-btn icon @click="moveDown(index)" :disabled="index === buttonConfigs.length - 1">
            <v-icon>mdi-arrow-down</v-icon>
          </v-btn>
          <v-btn icon @click="removeConfig(index)">
            <v-icon>mdi-delete</v-icon>
          </v-btn>
        </div>
      </div>

      <div class="config-fields">
        <Keyboard v-model="item.label" label="按钮标签" outlined dense />
        <Keyboard v-model="item.index" label="ID" outlined dense />
        <Keyboard v-model="item.name" label="字段名称" outlined dense />
        <v-select
          v-model="item.mode"
          :items="modeOptions"
          label="操作模式"
          outlined
          dense
          item-title="label"
          item-value="value"
        />
        <v-select
          v-model="item.type"
          :items="typeOptions"
          label="按钮类型"
          outlined
          dense
          item-title="label"
          item-value="value"
        />
        <v-select
          v-model="item.icon"
          :items="iconOptions"
          label="图标选择"
          outlined
          dense
          item-title="label"
          item-value="value"
        />
        <div v-if="item.type === 3" class="layout-config">
          <div v-for="(layout, layoutIndex) in item.layout" :key="layoutIndex" class="layout-item">
            <v-select
              v-model="item.layout[layoutIndex]"
              :items="layoutList"
              :label="`布局 ${layoutIndex + 1} 选择`"
              outlined
              dense
              item-title="name"
              :return-object="true"
            />
            <v-btn icon small @click="removeLayout(item, layoutIndex)">
              <v-icon>mdi-delete</v-icon>
            </v-btn>
          </div>
          <v-btn
            v-if="item.layout.length < 4"
            class="mt-2"
            color="primary"
            outlined
            @click="addLayout(item)"
          >
            <v-icon left>mdi-plus</v-icon>
            添加布局
          </v-btn>
        </div>
      </div>
    </div>

    <v-btn class="mt-4" color="primary" @click="addNewConfig">
      <v-icon left>mdi-plus</v-icon>
      新增配置
    </v-btn>

    <v-btn class="mt-4 ml-4" color="success" @click="saveConfigurations">
      <v-icon left>mdi-content-save</v-icon>
      保存配置
    </v-btn>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from "vue";
import { BUTTON_MODES, BUTTON_TYPES, ICON_OPTIONS } from "@/utils/constants";
import { ipc } from "@/utils/ipcRenderer";
import Keyboard from "@/components/Keyboard/index.vue";
import BackButton from "@/components/BackButton/index.vue";
import { getLayoutList } from "@/api/omsApi";
import notificationService from "@/utils/notificationService";

const buttonConfigs = ref([]);
const layoutList = ref([]);

const modeOptions = computed(() => BUTTON_MODES);
const typeOptions = computed(() => BUTTON_TYPES);
const iconOptions = computed(() => ICON_OPTIONS);

// 初始化加载配置
const loadConfigurations = async () => {
  const configs = await ipc.invoke("global-state:get", "pad.btnList");
  console.log(configs);

  buttonConfigs.value = configs.map((item) => ({
    ...item,
    // 确保默认值处理
    value: item.value ?? false,
    layout: item.layout ? JSON.parse(item.layout) : [],
  }));
};

// 添加新配置
const addNewConfig = () => {
  buttonConfigs.value.push({
    index: "",
    label: "新按钮",
    mode: "toggle",
    name: "",
    type: 1,
    icon: "mdi-circle",
    value: false,
    layout: [],
  });
};

// 删除配置
const removeConfig = (index) => {
  buttonConfigs.value.splice(index, 1);
};

// 保存配置
const saveConfigurations = async () => {
  // 深度克隆并清理数据，确保可序列化
  const cleanConfigs = buttonConfigs.value.map((config) => ({
    index: config.index,
    label: config.label,
    mode: config.mode,
    name: config.name,
    type: config.type,
    icon: config.icon,
    value: config.value ?? false,
    layout: JSON.stringify(config.layout),
  }));

  await ipc.invoke("global-state:set", "pad.btnList", cleanConfigs);
};

onMounted(async () => {
  const vID = await ipc.invoke("global-state:get", "vehicle_id");

  if (vID) {
    const {
      data: { lists },
    } = await getLayoutList(vID);
    if (lists.length) {
      layoutList.value = lists;
    }
  } else {
    notificationService.error("车辆ID获取失败");
  }
  loadConfigurations();
});

// 上移配置项
const moveUp = (index) => {
  if (index > 0) {
    const temp = buttonConfigs.value[index];
    buttonConfigs.value[index] = buttonConfigs.value[index - 1];
    buttonConfigs.value[index - 1] = temp;
  }
};

// 下移配置项
const moveDown = (index) => {
  if (index < buttonConfigs.value.length - 1) {
    const temp = buttonConfigs.value[index];
    buttonConfigs.value[index] = buttonConfigs.value[index + 1];
    buttonConfigs.value[index + 1] = temp;
  }
};

// 添加布局
const addLayout = (item) => {
  if (item.layout.length < 4) {
    item.layout.push({});
  }
};

// 删除布局
const removeLayout = (item, index) => {
  item.layout.splice(index, 1);
};
</script>

<style scoped>
.config-container {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;
}

.config-item {
  padding: 16px;
  margin-bottom: 16px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  transition: box-shadow 0.3s;
}

.config-item:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.config-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid #eee;
}

.config-fields {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 16px;
}

.layout-config {
  grid-column: 1 / -1; /* Span across all columns */
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.layout-item {
  display: flex;
  align-items: center;
  gap: 8px;
}
</style>
