console.log('[BUX-AUX-DEBUG] Main process script started.');

// =================================================================================
// 在 ee-core 初始化之前添加硬件加速相关的命令行开关
const { app } = require('electron');

// 必须写在 app.whenReady() 之前
app.commandLine.appendSwitch('enable-gpu-rasterization');
app.commandLine.appendSwitch('enable-zero-copy');
app.commandLine.appendSwitch('ignore-gpu-blacklist'); // 忽略黑名单
// 如需显式指定驱动
app.commandLine.appendSwitch('use-gl', 'egl');        // RK3399 用 EGL
// =================================================================================

const { ElectronEgg } = require('ee-core');
const { Lifecycle } = require('./preload/lifecycle');
const { preload } = require('./preload');

// new app
const eeApp = new ElectronEgg();

// register lifecycle
const life = new Lifecycle();
eeApp.register("ready", life.ready);
eeApp.register("electron-app-ready", life.electronAppReady);
eeApp.register("window-ready", life.windowReady);
eeApp.register("before-close", life.beforeClose);

// register preload
eeApp.register("preload", preload);

// run
eeApp.run();
