<script setup>
import GlobalNotification from "@/components/GlobalNotification/index.vue";
import GlobalLoading from "@/components/GlobalLoading/index.vue";
</script>

<template>
  <v-app>
    <v-main style="--v-layout-top: 0px; --v-layout-bottom: 0px">
      <router-view v-slot="{ Component }">
        <v-fade-transition mode="out-in">
          <component :is="Component" />
        </v-fade-transition>
      </router-view>
    </v-main>
    <!-- 全局通知组件 -->
    <GlobalNotification />
    <!-- 全局加载组件 -->
    <GlobalLoading />
  </v-app>
</template>

<style scoped></style>
