import { createApp } from "vue";
import "./style.css";
import App from "./App.vue";

// Vuetify
import "vuetify/styles";
import { createVuetify } from "vuetify";
import * as components from "vuetify/components";
import * as directives from "vuetify/directives";
import "@mdi/font/css/materialdesignicons.css";
import { zhHans, en, ja } from "vuetify/locale";
import { i18n } from "./i18n";
import router from "./router";

import { initVConsole } from "./utils/vconsole";
import { initWsMessageListener } from "@/utils/ipcMessageHandler";
import { initializeConfig } from "./api/config";

initVConsole();
initWsMessageListener();
initializeConfig();

const localeMap = {
  zh: "zhHans",
  en: "en",
  ja: "ja",
};
const vuetify = createVuetify({
  components,
  directives,
  locale: {
    locale: localeMap[i18n.global.locale.value] || "en",
    fallback: "en",
    messages: { zhHans, en, ja },
  },
  theme: {
    defaultTheme: "dark",
    themes: {
      light: {
        dark: false,
        colors: {
          primary: "#ff5f00",
          secondary: "#ff8f4d",
          background: "#f0f0f0",
          surface: "#f0f2f5",
        },
      },
      dark: {
        dark: true,
        colors: {
          primary: "#ff5f00",
          secondary: "#424242",
          background: "#0e0e0e",
          surface: "#212121",
        },
      },
    },
  },
});

const app = createApp(App);
app.use(vuetify);
app.use(i18n);
app.use(router);
app.mount("#app");

