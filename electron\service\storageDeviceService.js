'use strict';

const { EventEmitter } = require('events');
const { logger } = require('ee-core/log');
const os = require('os');
const fs = require('fs');
const path = require('path');
const { exec } = require('child_process');
const { promisify } = require('util');

const execAsync = promisify(exec);

/**
 * 存储设备检测服务
 * 支持Windows和Linux系统的U盘和存储设备检测
 */
class StorageDeviceService extends EventEmitter {
  static instance = null;

  constructor() {
    super();
    if (StorageDeviceService.instance) {
      return StorageDeviceService.instance;
    }

    this.isMonitoring = false;
    this.devices = new Map(); // 存储设备信息缓存
    this.monitorInterval = null;
    this.platform = os.platform();
    
    // 监控间隔时间（毫秒）
    this.MONITOR_INTERVAL = 2000;
    
    StorageDeviceService.instance = this;
    logger.info('[StorageDeviceService] Service initialized');
  }

  /**
   * 获取单例实例
   */
  static getInstance() {
    if (!StorageDeviceService.instance) {
      StorageDeviceService.instance = new StorageDeviceService();
    }
    return StorageDeviceService.instance;
  }

  /**
   * 启动设备监控
   */
  async startMonitoring() {
    if (this.isMonitoring) {
      logger.warn('[StorageDeviceService] Monitoring already started');
      return;
    }

    try {
      this.isMonitoring = true;
      
      // 初始扫描设备
      await this._scanDevices();
      
      // 启动定时监控
      this.monitorInterval = setInterval(async () => {
        try {
          await this._scanDevices();
        } catch (error) {
          logger.error('[StorageDeviceService] Error during device scan:', error);
        }
      }, this.MONITOR_INTERVAL);

      logger.info('[StorageDeviceService] Device monitoring started');
      this.emit('monitoring-started');
    } catch (error) {
      this.isMonitoring = false;
      logger.error('[StorageDeviceService] Failed to start monitoring:', error);
      throw error;
    }
  }

  /**
   * 停止设备监控
   */
  stopMonitoring() {
    if (!this.isMonitoring) {
      return;
    }

    this.isMonitoring = false;
    
    if (this.monitorInterval) {
      clearInterval(this.monitorInterval);
      this.monitorInterval = null;
    }

    logger.info('[StorageDeviceService] Device monitoring stopped');
    this.emit('monitoring-stopped');
  }

  /**
   * 获取所有可用的存储设备
   * @returns {Promise<Array>} 设备列表
   */
  async getDevices() {
    try {
      if (!this.isMonitoring) {
        await this._scanDevices();
      }
      
      return Array.from(this.devices.values());
    } catch (error) {
      logger.error('[StorageDeviceService] Failed to get devices:', error);
      return [];
    }
  }

  /**
   * 扫描存储设备
   * @private
   */
  async _scanDevices() {
    try {
      let newDevices;
      
      if (this.platform === 'win32') {
        newDevices = await this._scanWindowsDevices();
      } else if (this.platform === 'linux') {
        newDevices = await this._scanLinuxDevices();
      } else {
        logger.warn(`[StorageDeviceService] Unsupported platform: ${this.platform}`);
        return;
      }

      // 检测设备变化
      await this._detectDeviceChanges(newDevices);
      
    } catch (error) {
      logger.error('[StorageDeviceService] Error scanning devices:', error);
    }
  }

  /**
   * Windows系统设备扫描
   * @private
   */
  async _scanWindowsDevices() {
    try {
      // 使用wmic命令获取逻辑磁盘信息
      const { stdout } = await execAsync('wmic logicaldisk get size,freespace,caption,drivetype,volumename /format:csv');
      
      const devices = [];
      const lines = stdout.split('\n').filter(line => line.trim() && !line.startsWith('Node'));
      
      for (const line of lines) {
        const parts = line.split(',').map(part => part.trim());
        if (parts.length >= 6) {
          const [, caption, driveType, freeSpace, size, volumeName] = parts;
          
          if (caption && size && parseInt(size) > 0) {
            const device = {
              id: caption,
              label: caption,
              mountPoint: caption,
              type: this._getWindowsDriveType(parseInt(driveType)),
              totalSize: parseInt(size) || 0,
              freeSpace: parseInt(freeSpace) || 0,
              volumeName: volumeName || '',
              isRemovable: parseInt(driveType) === 2, // 2 = Removable disk
              platform: 'win32'
            };
            
            // 计算已用空间
            device.usedSpace = device.totalSize - device.freeSpace;
            
            devices.push(device);
          }
        }
      }
      
      return devices;
    } catch (error) {
      logger.error('[StorageDeviceService] Error scanning Windows devices:', error);
      return [];
    }
  }

  /**
   * Linux系统设备扫描
   * @private
   */
  async _scanLinuxDevices() {
    try {
      // 使用df命令获取挂载的文件系统信息
      const { stdout } = await execAsync('df -h --output=source,target,size,used,avail,pcent,fstype');
      
      const devices = [];
      const lines = stdout.split('\n').slice(1); // 跳过标题行
      
      for (const line of lines) {
        const parts = line.trim().split(/\s+/);
        if (parts.length >= 7) {
          const [source, target, size, used, avail, , fstype] = parts;
          
          // 过滤掉系统文件系统和虚拟文件系统
          if (this._isValidLinuxDevice(source, target, fstype)) {
            const device = {
              id: source,
              label: path.basename(target) || target,
              mountPoint: target,
              type: this._getLinuxDeviceType(source, target),
              totalSize: this._parseLinuxSize(size),
              usedSpace: this._parseLinuxSize(used),
              freeSpace: this._parseLinuxSize(avail),
              volumeName: path.basename(target),
              isRemovable: this._isRemovableLinuxDevice(source),
              platform: 'linux',
              filesystem: fstype
            };
            
            devices.push(device);
          }
        }
      }
      
      return devices;
    } catch (error) {
      logger.error('[StorageDeviceService] Error scanning Linux devices:', error);
      return [];
    }
  }

  /**
   * 检测设备变化
   * @private
   */
  async _detectDeviceChanges(newDevices) {
    const newDeviceMap = new Map();

    // 构建新设备映射
    newDevices.forEach(device => {
      newDeviceMap.set(device.id, device);
    });

    // 检测新增设备
    for (const [id, device] of newDeviceMap) {
      if (!this.devices.has(id)) {
        logger.info(`[StorageDeviceService] Device connected: ${device.label} (${device.mountPoint})`);
        this.emit('device-connected', device);
      }
    }

    // 检测移除设备
    for (const [id, device] of this.devices) {
      if (!newDeviceMap.has(id)) {
        logger.info(`[StorageDeviceService] Device disconnected: ${device.label} (${device.mountPoint})`);
        this.emit('device-disconnected', device);
      }
    }

    // 更新设备缓存
    this.devices = newDeviceMap;
    this.emit('devices-updated', Array.from(this.devices.values()));
  }

  /**
   * 获取Windows驱动器类型
   * @private
   */
  _getWindowsDriveType(driveType) {
    const types = {
      0: 'unknown',
      1: 'invalid',
      2: 'removable', // U盘、软盘等
      3: 'fixed',     // 硬盘
      4: 'network',   // 网络驱动器
      5: 'cdrom',     // CD-ROM
      6: 'ramdisk'    // RAM磁盘
    };
    return types[driveType] || 'unknown';
  }

  /**
   * 判断Linux设备是否有效
   * @private
   */
  _isValidLinuxDevice(source, target, fstype) {
    // 排除系统和虚拟文件系统
    const excludeFilesystems = ['tmpfs', 'devtmpfs', 'sysfs', 'proc', 'cgroup', 'cgroup2', 'pstore', 'bpf', 'debugfs', 'tracefs', 'securityfs', 'hugetlbfs', 'mqueue', 'configfs'];
    const excludePaths = ['/dev', '/proc', '/sys', '/run', '/boot/efi'];

    if (excludeFilesystems.includes(fstype)) {
      return false;
    }

    if (excludePaths.some(path => target.startsWith(path))) {
      return false;
    }

    // 只包含真实的存储设备
    return source.startsWith('/dev/') || target === '/' || target.startsWith('/home') || target.startsWith('/media') || target.startsWith('/mnt');
  }

  /**
   * 获取Linux设备类型
   * @private
   */
  _getLinuxDeviceType(source, target) {
    if (target.startsWith('/media') || target.startsWith('/mnt')) {
      return 'removable';
    }
    if (target === '/') {
      return 'system';
    }
    if (source.includes('sd') || source.includes('nvme')) {
      return 'fixed';
    }
    return 'unknown';
  }

  /**
   * 判断Linux设备是否为可移动设备
   * @private
   */
  _isRemovableLinuxDevice(source) {
    // 简单判断：通常U盘会挂载在/media或/mnt下
    // 更准确的方法需要检查/sys/block/设备/removable文件
    return source.includes('/media') || source.includes('/mnt') || source.includes('usb');
  }

  /**
   * 解析Linux文件系统大小
   * @private
   */
  _parseLinuxSize(sizeStr) {
    if (!sizeStr || sizeStr === '-') return 0;

    const units = {
      'K': 1024,
      'M': 1024 * 1024,
      'G': 1024 * 1024 * 1024,
      'T': 1024 * 1024 * 1024 * 1024
    };

    const match = sizeStr.match(/^(\d+(?:\.\d+)?)(K|M|G|T)?$/i);
    if (!match) return 0;

    const [, size, unit] = match;
    const multiplier = units[unit?.toUpperCase()] || 1;

    return Math.round(parseFloat(size) * multiplier);
  }

  /**
   * 格式化文件大小
   * @param {number} bytes - 字节数
   * @returns {string} 格式化后的大小
   */
  formatSize(bytes) {
    if (bytes === 0) return '0 B';

    const units = ['B', 'KB', 'MB', 'GB', 'TB'];
    const k = 1024;
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + units[i];
  }

  /**
   * 检查设备是否可写
   * @param {string} mountPoint - 挂载点
   * @returns {Promise<boolean>} 是否可写
   */
  async isDeviceWritable(mountPoint) {
    try {
      // const testFile = path.join(mountPoint, '.write_test_' + Date.now());
      // await fs.promises.writeFile(testFile, 'test');
      // await fs.promises.unlink(testFile);
      return true;
    } catch (error) {
      return false;
    }
  }

  /**
   * 获取设备详细信息
   * @param {string} deviceId - 设备ID
   * @returns {Object|null} 设备信息
   */
  getDeviceInfo(deviceId) {
    return this.devices.get(deviceId) || null;
  }

  /**
   * 销毁服务
   */
  destroy() {
    this.stopMonitoring();
    this.devices.clear();
    this.removeAllListeners();
    StorageDeviceService.instance = null;
    logger.info('[StorageDeviceService] Service destroyed');
  }
}

module.exports = StorageDeviceService;
