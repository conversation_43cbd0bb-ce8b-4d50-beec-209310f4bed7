'use strict';

const { EventEmitter } = require('events');
const os = require('os');
const fs = require('fs');
const path = require('path');
const { exec } = require('child_process');
const { promisify } = require('util');

const execAsync = promisify(exec);

// 尝试导入USB库，如果失败则回退到轮询模式
let usb = null;
let logger = null;

try {
  usb = require('usb');
  console.log('[EnhancedStorageDeviceService] USB library loaded successfully');
} catch (error) {
  console.warn('[EnhancedStorageDeviceService] USB library not available, falling back to polling mode:', error.message);
}

// 使用console作为logger的简单替代
const log = {
  info: (...args) => console.log('[INFO]', ...args),
  warn: (...args) => console.warn('[WARN]', ...args),
  error: (...args) => console.error('[ERROR]', ...args)
};

/**
 * 增强的存储设备检测服务
 * 结合USB事件检测和文件系统扫描，提供更精确和全面的设备检测
 */
class EnhancedStorageDeviceService extends EventEmitter {
  static instance = null;

  constructor() {
    super();
    if (EnhancedStorageDeviceService.instance) {
      return EnhancedStorageDeviceService.instance;
    }

    this.isMonitoring = false;
    this.devices = new Map(); // 存储设备信息缓存
    this.usbDevices = new Map(); // USB设备缓存
    this.monitorInterval = null;
    this.platform = os.platform();
    this.usbAvailable = !!usb;
    
    // 监控间隔时间（毫秒）- 当有USB事件时减少轮询频率
    this.MONITOR_INTERVAL = this.usbAvailable ? 5000 : 2000;
    
    EnhancedStorageDeviceService.instance = this;
    log.info('[EnhancedStorageDeviceService] Service initialized', {
      platform: this.platform,
      usbAvailable: this.usbAvailable
    });
  }

  /**
   * 获取单例实例
   */
  static getInstance() {
    if (!EnhancedStorageDeviceService.instance) {
      EnhancedStorageDeviceService.instance = new EnhancedStorageDeviceService();
    }
    return EnhancedStorageDeviceService.instance;
  }

  /**
   * 启动设备监控
   */
  async startMonitoring() {
    if (this.isMonitoring) {
      log.warn('[EnhancedStorageDeviceService] Monitoring already started');
      return;
    }

    try {
      this.isMonitoring = true;
      
      // 设置USB事件监听（如果可用）
      if (this.usbAvailable) {
        this._setupUsbEventListeners();
      }
      
      // 初始扫描设备
      await this._scanDevices();
      
      // 启动定时监控（作为备用机制）
      this.monitorInterval = setInterval(async () => {
        try {
          await this._scanDevices();
        } catch (error) {
          log.error('[EnhancedStorageDeviceService] Error during device scan:', error);
        }
      }, this.MONITOR_INTERVAL);

      log.info('[EnhancedStorageDeviceService] Device monitoring started', {
        usbEvents: this.usbAvailable,
        pollingInterval: this.MONITOR_INTERVAL
      });
      this.emit('monitoring-started');
    } catch (error) {
      this.isMonitoring = false;
      log.error('[EnhancedStorageDeviceService] Failed to start monitoring:', error);
      throw error;
    }
  }

  /**
   * 停止设备监控
   */
  stopMonitoring() {
    if (!this.isMonitoring) {
      return;
    }

    this.isMonitoring = false;
    
    // 停止定时监控
    if (this.monitorInterval) {
      clearInterval(this.monitorInterval);
      this.monitorInterval = null;
    }

    // 移除USB事件监听
    if (this.usbAvailable) {
      this._removeUsbEventListeners();
    }

    log.info('[EnhancedStorageDeviceService] Device monitoring stopped');
    this.emit('monitoring-stopped');
  }

  /**
   * 设置USB事件监听器
   * @private
   */
  _setupUsbEventListeners() {
    if (!this.usbAvailable) return;

    try {
      // 检查USB库是否有事件监听功能
      if (typeof usb.on === 'function') {
        // 监听USB设备连接事件
        usb.on('attach', (device) => {
          log.info('[EnhancedStorageDeviceService] USB device attached:', {
            vendorId: device.deviceDescriptor?.idVendor,
            productId: device.deviceDescriptor?.idProduct
          });

          // 延迟扫描，等待系统识别设备
          setTimeout(async () => {
            await this._scanDevices();
          }, 1000);
        });

        // 监听USB设备断开事件
        usb.on('detach', (device) => {
          log.info('[EnhancedStorageDeviceService] USB device detached:', {
            vendorId: device.deviceDescriptor?.idVendor,
            productId: device.deviceDescriptor?.idProduct
          });

          // 延迟扫描，等待系统更新设备列表
          setTimeout(async () => {
            await this._scanDevices();
          }, 500);
        });

        // 启用热插拔事件但不阻止进程退出
        if (typeof usb.unrefHotplugEvents === 'function') {
          usb.unrefHotplugEvents();
        }

        log.info('[EnhancedStorageDeviceService] USB event listeners setup completed');
      } else {
        log.warn('[EnhancedStorageDeviceService] USB library does not support event listeners, using polling only');
      }
    } catch (error) {
      log.error('[EnhancedStorageDeviceService] Failed to setup USB event listeners:', error);
    }
  }

  /**
   * 移除USB事件监听器
   * @private
   */
  _removeUsbEventListeners() {
    if (!this.usbAvailable) return;

    try {
      usb.removeAllListeners('attach');
      usb.removeAllListeners('detach');
      log.info('[EnhancedStorageDeviceService] USB event listeners removed');
    } catch (error) {
      log.error('[EnhancedStorageDeviceService] Failed to remove USB event listeners:', error);
    }
  }

  /**
   * 获取所有可用的存储设备
   * @returns {Promise<Array>} 设备列表
   */
  async getDevices() {
    try {
      if (!this.isMonitoring) {
        await this._scanDevices();
      }
      
      // 按优先级排序：可移动设备优先
      const devices = Array.from(this.devices.values());
      return this._sortDevicesByPriority(devices);
    } catch (error) {
      log.error('[EnhancedStorageDeviceService] Failed to get devices:', error);
      return [];
    }
  }

  /**
   * 按优先级排序设备
   * @private
   */
  _sortDevicesByPriority(devices) {
    return devices.sort((a, b) => {
      // 1. 可移动设备优先
      if (a.isRemovable !== b.isRemovable) {
        return b.isRemovable - a.isRemovable;
      }
      
      // 2. 可写设备优先
      if (a.isWritable !== b.isWritable) {
        return b.isWritable - a.isWritable;
      }
      
      // 3. 按设备类型排序
      const typeOrder = { 'removable': 0, 'fixed': 1, 'system': 2, 'unknown': 3 };
      const aOrder = typeOrder[a.type] || 3;
      const bOrder = typeOrder[b.type] || 3;
      
      if (aOrder !== bOrder) {
        return aOrder - bOrder;
      }
      
      // 4. 按盘符/挂载点排序
      return a.mountPoint.localeCompare(b.mountPoint);
    });
  }

  /**
   * 扫描存储设备
   * @private
   */
  async _scanDevices() {
    try {
      let newDevices;
      
      if (this.platform === 'win32') {
        newDevices = await this._scanWindowsDevices();
      } else if (this.platform === 'linux') {
        newDevices = await this._scanLinuxDevices();
      } else {
        log.warn(`[EnhancedStorageDeviceService] Unsupported platform: ${this.platform}`);
        return;
      }

      // 增强设备信息
      for (const device of newDevices) {
        await this._enhanceDeviceInfo(device);
      }

      // 检测设备变化
      await this._detectDeviceChanges(newDevices);
      
    } catch (error) {
      log.error('[EnhancedStorageDeviceService] Error scanning devices:', error);
    }
  }

  /**
   * 增强设备信息
   * @private
   */
  async _enhanceDeviceInfo(device) {
    try {
      // 检查设备是否可写
      device.isWritable = await this.isDeviceWritable(device.mountPoint);
      
      // 添加推荐级别
      device.recommendLevel = this._calculateRecommendLevel(device);
      
      // 添加设备描述
      device.description = this._generateDeviceDescription(device);
      
      // 格式化大小信息
      device.formattedTotalSize = this.formatSize(device.totalSize);
      device.formattedFreeSpace = this.formatSize(device.freeSpace);
      device.formattedUsedSpace = this.formatSize(device.usedSpace);
      
    } catch (error) {
      log.error('[EnhancedStorageDeviceService] Failed to enhance device info:', error);
    }
  }

  /**
   * 计算设备推荐级别
   * @private
   */
  _calculateRecommendLevel(device) {
    let level = 0;
    
    // 可移动设备加分
    if (device.isRemovable) level += 50;
    
    // 可写设备加分
    if (device.isWritable) level += 30;
    
    // 根据可用空间加分
    if (device.freeSpace > 1024 * 1024 * 1024) level += 20; // >1GB
    else if (device.freeSpace > 100 * 1024 * 1024) level += 10; // >100MB
    
    // 系统盘减分
    if (device.type === 'system') level -= 20;
    
    return level;
  }

  /**
   * 生成设备描述
   * @private
   */
  _generateDeviceDescription(device) {
    const parts = [];

    if (device.isRemovable) {
      parts.push('可移动存储设备');
    } else if (device.type === 'system') {
      parts.push('系统磁盘');
    } else {
      parts.push('本地磁盘');
    }

    if (device.volumeName) {
      parts.push(`(${device.volumeName})`);
    }

    return parts.join(' ');
  }

  /**
   * Windows系统设备扫描
   * @private
   */
  async _scanWindowsDevices() {
    try {
      // 使用wmic命令获取逻辑磁盘信息
      const { stdout } = await execAsync('wmic logicaldisk get size,freespace,caption,drivetype,volumename /format:csv');

      const devices = [];
      const lines = stdout.split('\n').filter(line => line.trim() && !line.startsWith('Node'));

      for (const line of lines) {
        const parts = line.split(',').map(part => part.trim());
        if (parts.length >= 6) {
          const [, caption, driveType, freeSpace, size, volumeName] = parts;

          if (caption && size && parseInt(size) > 0) {
            const device = {
              id: caption,
              label: caption,
              mountPoint: caption,
              type: this._getWindowsDriveType(parseInt(driveType)),
              totalSize: parseInt(size) || 0,
              freeSpace: parseInt(freeSpace) || 0,
              volumeName: volumeName || '',
              isRemovable: parseInt(driveType) === 2, // 2 = Removable disk
              platform: 'win32'
            };

            // 计算已用空间
            device.usedSpace = device.totalSize - device.freeSpace;

            devices.push(device);
          }
        }
      }

      return devices;
    } catch (error) {
      log.error('[EnhancedStorageDeviceService] Error scanning Windows devices:', error);
      return [];
    }
  }

  /**
   * Linux系统设备扫描
   * @private
   */
  async _scanLinuxDevices() {
    try {
      // 使用df命令获取挂载的文件系统信息
      const { stdout } = await execAsync('df -h --output=source,target,size,used,avail,pcent,fstype');

      const devices = [];
      const lines = stdout.split('\n').slice(1); // 跳过标题行

      for (const line of lines) {
        const parts = line.trim().split(/\s+/);
        if (parts.length >= 7) {
          const [source, target, size, used, avail, , fstype] = parts;

          // 过滤掉系统文件系统和虚拟文件系统
          if (this._isValidLinuxDevice(source, target, fstype)) {
            const device = {
              id: source,
              label: this._getLinuxDeviceLabel(source, target),
              mountPoint: target,
              type: this._getLinuxDeviceType(source, target),
              totalSize: this._parseLinuxSize(size),
              usedSpace: this._parseLinuxSize(used),
              freeSpace: this._parseLinuxSize(avail),
              volumeName: path.basename(target),
              isRemovable: this._isRemovableLinuxDevice(source, target),
              platform: 'linux',
              filesystem: fstype
            };

            devices.push(device);
          }
        }
      }

      // 添加用户家目录（如果不在列表中）
      await this._addLinuxUserDirectories(devices);

      return devices;
    } catch (error) {
      log.error('[EnhancedStorageDeviceService] Error scanning Linux devices:', error);
      return [];
    }
  }

  /**
   * 添加Linux用户目录
   * @private
   */
  async _addLinuxUserDirectories(devices) {
    try {
      const homeDir = os.homedir();
      const homeExists = devices.some(device => device.mountPoint === homeDir);

      if (!homeExists && fs.existsSync(homeDir)) {
        const stats = await fs.promises.stat(homeDir);
        const device = {
          id: `home:${homeDir}`,
          label: '用户目录',
          mountPoint: homeDir,
          type: 'user',
          totalSize: 0, // 无法直接获取
          usedSpace: 0,
          freeSpace: 0,
          volumeName: '用户目录',
          isRemovable: false,
          platform: 'linux',
          filesystem: 'directory'
        };

        devices.push(device);
      }
    } catch (error) {
      log.error('[EnhancedStorageDeviceService] Failed to add user directories:', error);
    }
  }

  /**
   * 获取Linux设备标签
   * @private
   */
  _getLinuxDeviceLabel(source, target) {
    if (target === '/') return '根目录';
    if (target.startsWith('/home')) return '用户目录';
    if (target.startsWith('/media')) return `可移动设备 (${path.basename(target)})`;
    if (target.startsWith('/mnt')) return `挂载设备 (${path.basename(target)})`;
    return path.basename(target) || target;
  }

  /**
   * 检测设备变化
   * @private
   */
  async _detectDeviceChanges(newDevices) {
    const newDeviceMap = new Map();

    // 构建新设备映射
    newDevices.forEach(device => {
      newDeviceMap.set(device.id, device);
    });

    // 检测新增设备
    for (const [id, device] of newDeviceMap) {
      if (!this.devices.has(id)) {
        log.info(`[EnhancedStorageDeviceService] Device connected: ${device.label} (${device.mountPoint})`);
        this.emit('device-connected', device);
      }
    }

    // 检测移除设备
    for (const [id, device] of this.devices) {
      if (!newDeviceMap.has(id)) {
        log.info(`[EnhancedStorageDeviceService] Device disconnected: ${device.label} (${device.mountPoint})`);
        this.emit('device-disconnected', device);
      }
    }

    // 更新设备缓存
    this.devices = newDeviceMap;
    this.emit('devices-updated', Array.from(this.devices.values()));
  }

  // 继承原有的工具方法
  _getWindowsDriveType(driveType) {
    const types = {
      0: 'unknown',
      1: 'invalid',
      2: 'removable', // U盘、软盘等
      3: 'fixed',     // 硬盘
      4: 'network',   // 网络驱动器
      5: 'cdrom',     // CD-ROM
      6: 'ramdisk'    // RAM磁盘
    };
    return types[driveType] || 'unknown';
  }

  _isValidLinuxDevice(source, target, fstype) {
    // 排除系统和虚拟文件系统
    const excludeFilesystems = ['tmpfs', 'devtmpfs', 'sysfs', 'proc', 'cgroup', 'cgroup2', 'pstore', 'bpf', 'debugfs', 'tracefs', 'securityfs', 'hugetlbfs', 'mqueue', 'configfs'];
    const excludePaths = ['/dev', '/proc', '/sys', '/run', '/boot/efi'];

    if (excludeFilesystems.includes(fstype)) {
      return false;
    }

    if (excludePaths.some(path => target.startsWith(path))) {
      return false;
    }

    // 只包含真实的存储设备
    return source.startsWith('/dev/') || target === '/' || target.startsWith('/home') || target.startsWith('/media') || target.startsWith('/mnt');
  }

  _getLinuxDeviceType(source, target) {
    if (target.startsWith('/media') || target.startsWith('/mnt')) {
      return 'removable';
    }
    if (target === '/') {
      return 'system';
    }
    if (target.startsWith('/home')) {
      return 'user';
    }
    if (source.includes('sd') || source.includes('nvme')) {
      return 'fixed';
    }
    return 'unknown';
  }

  _isRemovableLinuxDevice(source, target) {
    // 检查是否为可移动设备
    return target.startsWith('/media') || target.startsWith('/mnt') || source.includes('usb');
  }

  _parseLinuxSize(sizeStr) {
    if (!sizeStr || sizeStr === '-') return 0;

    const units = {
      'K': 1024,
      'M': 1024 * 1024,
      'G': 1024 * 1024 * 1024,
      'T': 1024 * 1024 * 1024 * 1024
    };

    const match = sizeStr.match(/^(\d+(?:\.\d+)?)(K|M|G|T)?$/i);
    if (!match) return 0;

    const [, size, unit] = match;
    const multiplier = units[unit?.toUpperCase()] || 1;

    return Math.round(parseFloat(size) * multiplier);
  }

  // 继承原有的公共方法
  formatSize(bytes) {
    if (bytes === 0) return '0 B';

    const units = ['B', 'KB', 'MB', 'GB', 'TB'];
    const k = 1024;
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + units[i];
  }

  async isDeviceWritable(mountPoint) {
    try {
      // const testFile = path.join(mountPoint, '.write_test_' + Date.now());
      // await fs.promises.writeFile(testFile, 'test');
      // await fs.promises.unlink(testFile);
      return true;
    } catch (error) {
      return false;
    }
  }

  getDeviceInfo(deviceId) {
    return this.devices.get(deviceId) || null;
  }

  destroy() {
    this.stopMonitoring();
    this.devices.clear();
    this.usbDevices.clear();
    this.removeAllListeners();
    EnhancedStorageDeviceService.instance = null;
    log.info('[EnhancedStorageDeviceService] Service destroyed');
  }
}

module.exports = EnhancedStorageDeviceService;
