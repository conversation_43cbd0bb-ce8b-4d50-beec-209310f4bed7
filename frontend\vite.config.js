import { defineConfig } from "vite";
import vue from "@vitejs/plugin-vue";
import vuetify from "vite-plugin-vuetify";
import { fileURLToPath, URL } from "node:url";

// https://vite.dev/config/
export default defineConfig({
  base: "./",
  publicDir: "public",
  server: {
    host: "0.0.0.0",
    port: 8080,
    proxy: {
      "^/api/": {
        target: "http://***************:8080", // 线上
        // target: "http://127.0.0.1:8000", // 本地
        changeOrigin: true,
      },
    },
  },
  plugins: [vue(), vuetify()],
  resolve: {
    alias: {
      "@": fileURLToPath(new URL("./src", import.meta.url)),
    },
  },
  build: {
    outDir: "dist",
    assetsDir: "assets",
    sourcemap: false,
    minify: "terser",
    terserOptions: {
      compress: {
        drop_console: false,
        drop_debugger: true,
      },
    },
    rollupOptions: {
      output: {
        manualChunks: {
          vue: ["vue"],
          vuetify: ["vuetify"],
        },
      },
    },
  },
});
